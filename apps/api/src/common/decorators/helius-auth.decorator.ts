import { UseGuards } from '@nestjs/common';
import { HeliusWebhookGuard } from '../guards/helius-webhook.guard';

/**
 * Decorator to protect Helius webhook endpoints with webhook-specific token
 *
 * Usage:
 * @HeliusAuth()
 * @Post('helius')
 * receiveWebhook() { ... }
 *
 * Clients must include the Helius webhook token as:
 * - Authorization: Bearer <helius-webhook-token>
 */
export const HeliusAuth = () => UseGuards(HeliusWebhookGuard);
